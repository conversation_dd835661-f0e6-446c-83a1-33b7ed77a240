"""
Video processing utilities for lightweight VSR
Handles frame extraction, preprocessing, and augmentation
"""

import cv2
import numpy as np
import torch
from typing import List, Tuple, Optional, Union
import av
from pathlib import Path
import random


class VideoProcessor:
    """Video preprocessing pipeline for mouth-focused videos"""
    
    def __init__(self, 
                 target_frames: int = 32,
                 target_size: Tuple[int, int] = (96, 96),
                 grayscale: bool = True,
                 fps: Optional[float] = None):
        self.target_frames = target_frames
        self.target_size = target_size
        self.grayscale = grayscale
        self.fps = fps
    
    def extract_frames(self, video_path: Union[str, Path]) -> np.ndarray:
        """
        Extract frames from video using PyAV
        
        Args:
            video_path: Path to video file
            
        Returns:
            frames: numpy array of shape (frames, height, width, channels)
        """
        video_path = str(video_path)
        frames = []
        
        try:
            with av.open(video_path) as container:
                stream = container.streams.video[0]
                
                # Set target fps if specified
                if self.fps:
                    stream.codec_context.framerate = self.fps
                
                for frame in container.decode(stream):
                    # Convert to numpy array
                    img = frame.to_ndarray(format='rgb24')
                    
                    # Convert to grayscale if needed
                    if self.grayscale:
                        img = cv2.cvtColor(img, cv2.COLOR_RGB2GRAY)
                        img = np.expand_dims(img, axis=-1)
                    
                    frames.append(img)
                    
        except Exception as e:
            # Fallback to OpenCV
            print(f"PyAV failed for {video_path}, using OpenCV: {e}")
            return self._extract_frames_opencv(video_path)
        
        if not frames:
            raise ValueError(f"No frames extracted from {video_path}")
            
        return np.array(frames)
    
    def _extract_frames_opencv(self, video_path: str) -> np.ndarray:
        """Fallback frame extraction using OpenCV"""
        cap = cv2.VideoCapture(video_path)
        frames = []
        
        while True:
            ret, frame = cap.read()
            if not ret:
                break
                
            if self.grayscale:
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                frame = np.expand_dims(frame, axis=-1)
            else:
                frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                
            frames.append(frame)
        
        cap.release()
        
        if not frames:
            raise ValueError(f"No frames extracted from {video_path}")
            
        return np.array(frames)
    
    def resize_frames(self, frames: np.ndarray) -> np.ndarray:
        """Resize frames to target size"""
        resized_frames = []

        for frame in frames:
            if self.grayscale and len(frame.shape) == 3:
                # Handle grayscale frames with channel dimension
                if frame.shape[2] == 1:
                    resized = cv2.resize(frame[:, :, 0], self.target_size)
                else:
                    # Convert to grayscale first
                    gray = cv2.cvtColor(frame, cv2.COLOR_RGB2GRAY)
                    resized = cv2.resize(gray, self.target_size)
                resized = np.expand_dims(resized, axis=-1)
            elif self.grayscale and len(frame.shape) == 2:
                # Already grayscale, just resize
                resized = cv2.resize(frame, self.target_size)
                resized = np.expand_dims(resized, axis=-1)
            else:
                # Color frames
                resized = cv2.resize(frame, self.target_size)
            resized_frames.append(resized)

        return np.array(resized_frames)
    
    def temporal_crop_or_pad(self, frames: np.ndarray) -> np.ndarray:
        """Crop or pad frames to target temporal length"""
        num_frames = len(frames)
        
        if num_frames == self.target_frames:
            return frames
        elif num_frames > self.target_frames:
            # Crop from center
            start_idx = (num_frames - self.target_frames) // 2
            return frames[start_idx:start_idx + self.target_frames]
        else:
            # Pad by repeating last frame
            padding_needed = self.target_frames - num_frames
            last_frame = frames[-1:] if num_frames > 0 else np.zeros_like(frames[:1])
            padding = np.repeat(last_frame, padding_needed, axis=0)
            return np.concatenate([frames, padding], axis=0)
    
    def normalize(self, frames: np.ndarray) -> np.ndarray:
        """Normalize frames to [0, 1] range"""
        return frames.astype(np.float32) / 255.0
    
    def process_video(self, video_path: Union[str, Path]) -> torch.Tensor:
        """
        Complete video processing pipeline
        
        Args:
            video_path: Path to video file
            
        Returns:
            Processed video tensor of shape (1, frames, height, width) for grayscale
            or (3, frames, height, width) for RGB
        """
        # Extract frames
        frames = self.extract_frames(video_path)
        
        # Resize frames
        frames = self.resize_frames(frames)
        
        # Temporal crop/pad
        frames = self.temporal_crop_or_pad(frames)
        
        # Normalize
        frames = self.normalize(frames)
        
        # Convert to tensor and rearrange dimensions
        # From (T, H, W, C) to (C, T, H, W)
        frames_tensor = torch.from_numpy(frames).permute(3, 0, 1, 2)
        
        return frames_tensor


class VideoAugmentation:
    """Data augmentation for video training"""
    
    def __init__(self,
                 brightness_range: float = 0.15,
                 contrast_range: float = 0.15,
                 scale_range: float = 0.10,
                 vertical_jitter: int = 8,
                 temporal_jitter: int = 4):
        self.brightness_range = brightness_range
        self.contrast_range = contrast_range
        self.scale_range = scale_range
        self.vertical_jitter = vertical_jitter
        self.temporal_jitter = temporal_jitter
    
    def apply_brightness_contrast(self, frames: torch.Tensor) -> torch.Tensor:
        """Apply random brightness and contrast changes"""
        if random.random() < 0.5:
            # Brightness
            brightness_factor = 1.0 + random.uniform(-self.brightness_range, self.brightness_range)
            frames = frames * brightness_factor
            
            # Contrast
            contrast_factor = 1.0 + random.uniform(-self.contrast_range, self.contrast_range)
            mean = frames.mean()
            frames = (frames - mean) * contrast_factor + mean
            
            # Clamp to valid range
            frames = torch.clamp(frames, 0.0, 1.0)
        
        return frames
    
    def apply_scale_jitter(self, frames: torch.Tensor) -> torch.Tensor:
        """Apply random scaling"""
        if random.random() < 0.5:
            scale_factor = 1.0 + random.uniform(-self.scale_range, self.scale_range)
            
            # Get current size
            _, T, H, W = frames.shape
            new_H, new_W = int(H * scale_factor), int(W * scale_factor)
            
            # Resize
            frames = torch.nn.functional.interpolate(
                frames.unsqueeze(0), 
                size=(T, new_H, new_W), 
                mode='trilinear', 
                align_corners=False
            ).squeeze(0)
            
            # Crop or pad back to original size
            if new_H > H or new_W > W:
                # Crop from center
                start_h = (new_H - H) // 2
                start_w = (new_W - W) // 2
                frames = frames[:, :, start_h:start_h+H, start_w:start_w+W]
            elif new_H < H or new_W < W:
                # Pad
                pad_h = (H - new_H) // 2
                pad_w = (W - new_W) // 2
                frames = torch.nn.functional.pad(
                    frames,
                    (pad_w, W-new_W-pad_w, pad_h, H-new_H-pad_h),
                    mode='replicate'
                )
        
        return frames
    
    def apply_vertical_jitter(self, frames: torch.Tensor) -> torch.Tensor:
        """Apply random vertical translation"""
        if random.random() < 0.5:
            shift = random.randint(-self.vertical_jitter, self.vertical_jitter)
            if shift != 0:
                _, T, H, W = frames.shape
                if shift > 0:
                    # Shift down
                    frames = torch.cat([
                        frames[:, :, shift:, :],
                        frames[:, :, -shift:, :].repeat(1, 1, shift, 1)
                    ], dim=2)
                else:
                    # Shift up
                    shift = abs(shift)
                    frames = torch.cat([
                        frames[:, :, :shift, :].repeat(1, 1, shift, 1),
                        frames[:, :, :-shift, :]
                    ], dim=2)
        
        return frames
    
    def apply_temporal_jitter(self, frames: torch.Tensor) -> torch.Tensor:
        """Apply random temporal shifts"""
        if random.random() < 0.5:
            shift = random.randint(-self.temporal_jitter, self.temporal_jitter)
            if shift != 0:
                C, T, H, W = frames.shape
                if shift > 0:
                    # Shift forward in time
                    frames = torch.cat([
                        frames[:, shift:, :, :],
                        frames[:, -shift:, :, :].repeat(1, shift, 1, 1)
                    ], dim=1)
                else:
                    # Shift backward in time
                    shift = abs(shift)
                    frames = torch.cat([
                        frames[:, :shift, :, :].repeat(1, shift, 1, 1),
                        frames[:, :-shift, :, :]
                    ], dim=1)
        
        return frames
    
    def __call__(self, frames: torch.Tensor) -> torch.Tensor:
        """Apply all augmentations"""
        frames = self.apply_brightness_contrast(frames)
        frames = self.apply_scale_jitter(frames)
        frames = self.apply_vertical_jitter(frames)
        frames = self.apply_temporal_jitter(frames)
        return frames


def create_video_processor(config: dict) -> VideoProcessor:
    """Factory function to create VideoProcessor from config"""
    return VideoProcessor(
        target_frames=config.get('frames', 32),
        target_size=(config.get('height', 96), config.get('width', 96)),
        grayscale=config.get('grayscale', True)
    )


def create_augmentation(config: dict) -> VideoAugmentation:
    """Factory function to create VideoAugmentation from config"""
    return VideoAugmentation(
        brightness_range=config.get('brightness_contrast_range', 0.15),
        contrast_range=config.get('brightness_contrast_range', 0.15),
        scale_range=config.get('scale_range', 0.10),
        vertical_jitter=config.get('vertical_jitter', 8),
        temporal_jitter=config.get('temporal_jitter', 4)
    )


if __name__ == "__main__":
    # Test video processing
    processor = VideoProcessor()

    # Test with a sample video (if available)
    test_video = "test_webm_videos/sample.mp4"
    if Path(test_video).exists():
        try:
            frames = processor.process_video(test_video)
            print(f"Processed video shape: {frames.shape}")
            print(f"Frame range: [{frames.min():.3f}, {frames.max():.3f}]")
        except Exception as e:
            print(f"Error processing video: {e}")
    else:
        print("No test video found, skipping test")
